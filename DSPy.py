import dspy
import os

if __name__ == '__main__':
    from dspy import Predict, ChainOfThought

    # Configure DSPy with a language model
    # Option 1: Using OpenAI (requires API key)
    # Uncomment the following lines and set your OpenAI API key
    # os.environ["OPENAI_API_KEY"] = "your-api-key-here"
    # lm = dspy.LM('openai/gpt-4o-mini')

    # Option 2: Using a local model (Ollama example)
    # Make sure you have Ollama installed and running
    # lm = dspy.LM('ollama/llama2', api_base='http://localhost:11434')

    # Option 3: Using a mock/dummy model for testing
    # This will use a simple pattern-based response
    lm = dspy.LM('openai/gpt-4o-mini',
                 api_key='********************************************************************************************************************************************************************')

    try:
        # Configure DSPy with the language model
        dspy.configure(lm=lm)

        # Create the capital finder
        capital_finder = ChainOfThought("country -> capital")

        # Test with France
        result = capital_finder(country="France")
        print(f"The capital of France is: {result.capital}")

        # Test with a few more countries
        countries = ["Germany", "Japan", "Brazil", "India"]
        for country in countries:
            result = capital_finder(country=country)
            print(f"The capital of {country} is: {result.capital}")

    except Exception as e:
        print(f"Error: {e}")
        print("\nTo run this program successfully, you need to:")
        print("1. Set up an OpenAI API key, OR")
        print("2. Install and run Ollama with a local model, OR")
        print("3. Set up another compatible language model")
        print("\nFor OpenAI:")
        print("- Get an API key from https://platform.openai.com/")
        print("- Uncomment the OpenAI configuration lines in the code")
        print("- Replace 'your-api-key-here' with your actual API key")
