import dspy
import os

if __name__ == '__main__':
    from dspy import Predict, ChainOfThought

    # Configure DSPy with a language model
    # Option 1: Using OpenAI (requires API key)
    # Uncomment the following lines and set your OpenAI API key
    # os.environ["OPENAI_API_KEY"] = "your-api-key-here"
    # lm = dspy.LM('openai/gpt-4o-mini')

    # Option 2: Using a local model (Ollama example)
    # Make sure you have Ollama installed and running
    # lm = dspy.LM('ollama/llama2', api_base='http://localhost:11434')

    # Option 3: Using a mock/dummy model for testing
    # This will use a simple pattern-based response
    lm = dspy.LM('openai/gpt-4o-mini',
                 api_key='********************************************************************************************************************************************************************')

    try:
        # Configure DSPy with the language model
        dspy.configure(lm=lm)

        print("=== COMPARING Predict vs ChainOfThought ===\n")

        # Create both types of predictors
        basic_predictor = Predict("country -> capital")
        cot_predictor = ChainOfThought("country -> capital")

        # Test with a simple case first
        print("1. SIMPLE CASE - Well-known capitals:")
        print("-" * 40)

        country = "France"
        basic_result = basic_predictor(country=country)
        cot_result = cot_predictor(country=country)

        print(f"Country: {country}")
        print(f"Predict result: {basic_result.capital}")
        print(f"ChainOfThought result: {cot_result.capital}")
        if hasattr(cot_result, 'rationale'):
            print(f"ChainOfThought reasoning: {cot_result.rationale}")
        print()

        # Test with more complex cases where reasoning matters
        print("2. COMPLEX CASES - Where reasoning helps:")
        print("-" * 40)

        complex_cases = [
            "What is the capital of the country that has the Eiffel Tower?",
            "Which city serves as the capital of the nation known for sushi and Mount Fuji?",
            "What's the capital of the largest country in South America?",
            "Which country have Taj Mahal?"
        ]

        for case in complex_cases:
            print(f"Question: {case}")
            try:
                # For complex questions, we need to modify the signature
                complex_basic = Predict("question -> answer")
                complex_cot = ChainOfThought("question -> answer")

                basic_result = complex_basic(question=case)
                cot_result = complex_cot(question=case)

                print(f"Predict: {basic_result.answer}")
                print(f"ChainOfThought: {cot_result.answer}")
                if hasattr(cot_result, 'rationale'):
                    print(f"Reasoning: {cot_result.rationale}")
                print()
            except Exception as e:
                print(f"Error with complex case: {e}")
                print()

        # Test with ambiguous cases
        print("3. AMBIGUOUS CASES - Multiple possible answers:")
        print("-" * 40)

        ambiguous_predictor = ChainOfThought("country_description -> capital, reasoning")

        ambiguous_cases = [
            "A country that was divided after WWII and reunified in 1990",
            "The country that spans two continents and has both European and Asian parts",
            "Which city is IT hub for India?"
        ]

        for case in ambiguous_cases:
            print(f"Description: {case}")
            try:
                result = ambiguous_predictor(country_description=case)
                print(f"Capital: {result.capital}")
                if hasattr(result, 'reasoning'):
                    print(f"Reasoning: {result.reasoning}")
                print()
            except Exception as e:
                print(f"Error: {e}")
                print()

        print("4. ORIGINAL SIMPLE TEST:")
        print("-" * 40)
        countries = ["Germany", "Japan", "Brazil", "India"]
        for country in countries:
            result = cot_predictor(country=country)
            print(f"The capital of {country} is: {result.capital}")

    except Exception as e:
        print(f"Error: {e}")
        print("\nTo run this program successfully, you need to:")
        print("1. Set up an OpenAI API key, OR")
        print("2. Install and run Ollama with a local model, OR")
        print("3. Set up another compatible language model")
        print("\nFor OpenAI:")
        print("- Get an API key from https://platform.openai.com/")
        print("- Uncomment the OpenAI configuration lines in the code")
        print("- Replace 'your-api-key-here' with your actual API key")
